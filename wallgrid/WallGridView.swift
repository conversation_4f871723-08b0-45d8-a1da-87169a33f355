import SwiftUI
import AppKit

struct WallGridView: View {
    @State private var wallpaperBoxes: [WallpaperBox] = Array(0..<10).map { WallpaperBox(id: $0) }

    var body: some View {
        VStack(spacing: 0) {
            // شريط علوي مع زر إضافة متعددة
            HStack {
                Button(action: {
                    addMultipleImages()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "plus.rectangle.on.folder")
                            .font(.system(size: 12))
                        Text("إضافة متعددة")
                            .font(.system(size: 11))
                    }
                    .foregroundColor(.blue)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(6)
                }
                .buttonStyle(PlainButtonStyle())
                .help("اختيار عدة صور وإضافتها دفعة واحدة")

                Spacer()
            }
            .padding(.horizontal, 15)
            .padding(.top, 8)

            ScrollView(showsIndicators: false) {
                LazyVGrid(columns: [
                    GridItem(.fixed(180), spacing: 10),
                    GridItem(.fixed(180), spacing: 10)
                ], spacing: 10) {
                    ForEach(wallpaperBoxes.indices, id: \.self) { index in
                        GridBoxView(wallpaperBox: $wallpaperBoxes[index])
                    }
                }
            }
            .cornerRadius(35)
            .padding(.horizontal, 10)
            .padding(.bottom, 10)
        }
        .frame(width: 390, height: 290)
        .background(
            VisualEffectView(material: .hudWindow, blendingMode: .behindWindow)
        )
        .cornerRadius(40)
    }

    private func addMultipleImages() {
        let openPanel = NSOpenPanel()
        openPanel.title = "اختر صور متعددة"
        openPanel.canChooseFiles = true
        openPanel.canChooseDirectories = false
        openPanel.allowsMultipleSelection = true
        openPanel.allowedContentTypes = [.image]

        openPanel.begin { response in
            if response == .OK {
                let selectedURLs = openPanel.urls
                addImagesToEmptyBoxes(urls: selectedURLs)
            }
        }
    }

    private func addImagesToEmptyBoxes(urls: [URL]) {
        var urlIndex = 0

        for boxIndex in wallpaperBoxes.indices {
            // التحقق من أن المربع فارغ وأن لدينا صور متبقية
            if wallpaperBoxes[boxIndex].selectedImage == nil && urlIndex < urls.count {
                let url = urls[urlIndex]

                if let image = NSImage(contentsOf: url) {
                    let optimizedImage = createHighQualityThumbnail(from: image)
                    wallpaperBoxes[boxIndex].selectedImage = optimizedImage
                    wallpaperBoxes[boxIndex].imagePath = url.path
                }

                urlIndex += 1
            }

            // إذا انتهت الصور، توقف
            if urlIndex >= urls.count {
                break
            }
        }

        // إظهار رسالة إذا كانت هناك صور أكثر من المربعات الفارغة
        if urlIndex < urls.count {
            let remainingImages = urls.count - urlIndex
            print("تم إضافة \(urlIndex) صور. تبقى \(remainingImages) صور لم يتم إضافتها لعدم وجود مربعات فارغة.")
        }
    }

    private func createHighQualityThumbnail(from image: NSImage) -> NSImage {
        let targetSize = NSSize(width: 360, height: 240)
        let imageSize = image.size

        let widthRatio = targetSize.width / imageSize.width
        let heightRatio = targetSize.height / imageSize.height
        let ratio = max(widthRatio, heightRatio)

        let newSize = NSSize(
            width: imageSize.width * ratio,
            height: imageSize.height * ratio
        )

        let thumbnail = NSImage(size: newSize)
        thumbnail.lockFocus()

        let context = NSGraphicsContext.current
        context?.imageInterpolation = .high
        context?.shouldAntialias = true
        context?.compositingOperation = .copy

        if let cgContext = context?.cgContext {
            cgContext.interpolationQuality = .high
            cgContext.setShouldAntialias(true)
            cgContext.setShouldSmoothFonts(true)
            cgContext.setAllowsAntialiasing(true)
            cgContext.setShouldSubpixelPositionFonts(true)
            cgContext.setShouldSubpixelQuantizeFonts(true)
        }

        image.draw(
            in: NSRect(origin: .zero, size: newSize),
            from: NSRect(origin: .zero, size: image.size),
            operation: .copy,
            fraction: 1.0
        )

        thumbnail.unlockFocus()
        return thumbnail
    }
}

struct WallpaperBox {
    let id: Int
    var selectedImage: NSImage?
    var imagePath: String?

    init(id: Int) {
        self.id = id
        self.selectedImage = nil
        self.imagePath = nil
    }
}

struct GridBoxView: View {
    @Binding var wallpaperBox: WallpaperBox
    @State private var isDragOver = false

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 35)
                .fill(Color.white.opacity(isDragOver ? 0.25 : 0.10))
                .frame(width: 180, height: 120)
                .overlay(
                    RoundedRectangle(cornerRadius: 35)
                        .stroke(isDragOver ? Color.blue.opacity(0.8) : Color.clear, lineWidth: 2)
                )

            if let selectedImage = wallpaperBox.selectedImage {
                // عرض الصورة المختارة بجودة عالية
                Image(nsImage: selectedImage)
                    .resizable()
                    .interpolation(.high)
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 180, height: 120)
                    .clipped()
                    .cornerRadius(35)
                    .drawingGroup()
                    .onTapGesture {
                        applyWallpaper()
                    }
            } else {
                // مربع فارغ
                VStack {
                    Image(systemName: "plus.circle")
                        .font(.title)
                        .foregroundColor(.blue)
                    Text("مربع \(wallpaperBox.id + 1)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .onTapGesture {
                    chooseImage()
                }
            }
        }
        .onDrop(of: [.image, .fileURL], isTargeted: $isDragOver) { providers in
            handleDrop(providers: providers)
        }
        .contextMenu {
            if wallpaperBox.selectedImage != nil {
                Button("تطبيق كخلفية") {
                    applyWallpaper()
                }
                Button("حذف الصورة") {
                    wallpaperBox.selectedImage = nil
                    wallpaperBox.imagePath = nil
                }
                Button("اختيار صورة جديدة") {
                    chooseImage()
                }
            } else {
                Button("اختيار صورة") {
                    chooseImage()
                }
            }
        }
    }

    private func handleDrop(providers: [NSItemProvider]) -> Bool {
        for provider in providers {
            // التعامل مع ملفات الصور
            if provider.hasItemConformingToTypeIdentifier("public.file-url") {
                _ = provider.loadObject(ofClass: URL.self) { url, error in
                    if let url = url, let image = NSImage(contentsOf: url) {
                        DispatchQueue.main.async {
                            let optimizedImage = self.createHighQualityThumbnail(from: image)
                            self.wallpaperBox.selectedImage = optimizedImage
                            self.wallpaperBox.imagePath = url.path
                        }
                    }
                }
                return true
            }

            // التعامل مع الصور المباشرة
            if provider.hasItemConformingToTypeIdentifier("public.image") {
                provider.loadDataRepresentation(forTypeIdentifier: "public.image") { data, error in
                    if let data = data, let image = NSImage(data: data) {
                        DispatchQueue.main.async {
                            let optimizedImage = GridBoxView.createHighQualityThumbnail(from: image)
                            self.wallpaperBox.selectedImage = optimizedImage
                            // لا يوجد مسار للصور المسحوبة مباشرة
                            self.wallpaperBox.imagePath = nil
                        }
                    }
                }
                return true
            }
        }
        return false
    }

    private func chooseImage() {
        let openPanel = NSOpenPanel()
        openPanel.title = "اختر صورة خلفية"
        openPanel.canChooseFiles = true
        openPanel.canChooseDirectories = false
        openPanel.allowsMultipleSelection = false
        openPanel.allowedContentTypes = [.image]

        openPanel.begin { response in
            if response == .OK, let url = openPanel.url {
                if let image = NSImage(contentsOf: url) {
                    // تحسين جودة الصورة للعرض
                    let optimizedImage = GridBoxView.createHighQualityThumbnail(from: image)
                    wallpaperBox.selectedImage = optimizedImage
                    wallpaperBox.imagePath = url.path
                }
            }
        }
    }

    static func createHighQualityThumbnail(from image: NSImage) -> NSImage {
        // حساب الحجم المناسب مع الحفاظ على النسبة
        let targetSize = NSSize(width: 360, height: 240) // ضعف الحجم للحصول على دقة أعلى
        let imageSize = image.size

        let widthRatio = targetSize.width / imageSize.width
        let heightRatio = targetSize.height / imageSize.height
        let ratio = max(widthRatio, heightRatio) // استخدام max للتأكد من ملء المساحة

        let newSize = NSSize(
            width: imageSize.width * ratio,
            height: imageSize.height * ratio
        )

        let thumbnail = NSImage(size: newSize)
        thumbnail.lockFocus()

        // إعدادات عالية الجودة
        let context = NSGraphicsContext.current
        context?.imageInterpolation = .high
        context?.shouldAntialias = true
        context?.compositingOperation = .copy

        // تحسين جودة الرسم باستخدام Core Graphics
        if let cgContext = context?.cgContext {
            cgContext.interpolationQuality = .high
            cgContext.setShouldAntialias(true)
            cgContext.setShouldSmoothFonts(true)
            cgContext.setAllowsAntialiasing(true)
            cgContext.setShouldSubpixelPositionFonts(true)
            cgContext.setShouldSubpixelQuantizeFonts(true)
        }

        // رسم الصورة بجودة عالية
        image.draw(
            in: NSRect(origin: .zero, size: newSize),
            from: NSRect(origin: .zero, size: image.size),
            operation: .copy,
            fraction: 1.0
        )

        thumbnail.unlockFocus()
        return thumbnail
    }

    private func applyWallpaper() {
        // إذا كان هناك مسار للصورة، استخدمه
        if let imagePath = wallpaperBox.imagePath {
            let url = URL(fileURLWithPath: imagePath)

            do {
                try NSWorkspace.shared.setDesktopImageURL(url, for: NSScreen.main!, options: [:])
                print("تم تطبيق الخلفية بنجاح!")
            } catch {
                print("خطأ في تطبيق الخلفية: \(error)")
            }
        }
        // إذا لم يكن هناك مسار (صورة مسحوبة)، إنشاء ملف مؤقت
        else if let selectedImage = wallpaperBox.selectedImage {
            createTemporaryImageFile(from: selectedImage) { tempPath in
                if let tempPath = tempPath {
                    let url = URL(fileURLWithPath: tempPath)
                    do {
                        try NSWorkspace.shared.setDesktopImageURL(url, for: NSScreen.main!, options: [:])
                        print("تم تطبيق الخلفية بنجاح!")
                    } catch {
                        print("خطأ في تطبيق الخلفية: \(error)")
                    }
                }
            }
        }
    }

    private func createTemporaryImageFile(from image: NSImage, completion: @escaping (String?) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            let tempDirectory = NSTemporaryDirectory()
            let fileName = "wallpaper_\(UUID().uuidString).png"
            let tempPath = tempDirectory + fileName

            if let tiffData = image.tiffRepresentation,
               let bitmapImage = NSBitmapImageRep(data: tiffData),
               let pngData = bitmapImage.representation(using: .png, properties: [:]) {

                do {
                    try pngData.write(to: URL(fileURLWithPath: tempPath))
                    DispatchQueue.main.async {
                        completion(tempPath)
                    }
                } catch {
                    print("خطأ في إنشاء الملف المؤقت: \(error)")
                    DispatchQueue.main.async {
                        completion(nil)
                    }
                }
            } else {
                DispatchQueue.main.async {
                    completion(nil)
                }
            }
        }
    }
}

// امتداد لتحسين جودة الصور
extension NSImage {
    /// إنشاء نسخة عالية الجودة من الصورة
    func highQualityRepresentation() -> NSImage {
        guard let tiffData = self.tiffRepresentation,
              let bitmapRep = NSBitmapImageRep(data: tiffData) else {
            return self
        }

        // إنشاء صورة جديدة بدقة أعلى
        let newImage = NSImage(size: self.size)
        newImage.addRepresentation(bitmapRep)

        return newImage
    }

    /// تحسين الصورة للعرض في الواجهة
    func optimizedForDisplay(targetSize: NSSize) -> NSImage {
        let optimized = NSImage(size: targetSize)
        optimized.lockFocus()

        // إعدادات متقدمة للجودة
        let context = NSGraphicsContext.current
        context?.imageInterpolation = .high
        context?.shouldAntialias = true
        context?.compositingOperation = .sourceOver

        // رسم بجودة عالية
        self.draw(
            in: NSRect(origin: .zero, size: targetSize),
            from: NSRect(origin: .zero, size: self.size),
            operation: .copy,
            fraction: 1.0
        )

        optimized.unlockFocus()
        return optimized
    }
}

// Visual Effect View لتطبيق تأثير blur
struct VisualEffectView: NSViewRepresentable {
    let material: NSVisualEffectView.Material
    let blendingMode: NSVisualEffectView.BlendingMode

    func makeNSView(context: Context) -> NSVisualEffectView {
        let visualEffectView = NSVisualEffectView()
        visualEffectView.material = material
        visualEffectView.blendingMode = blendingMode
        visualEffectView.state = .active
        return visualEffectView
    }

    func updateNSView(_ visualEffectView: NSVisualEffectView, context: Context) {
        visualEffectView.material = material
        visualEffectView.blendingMode = blendingMode
    }
}

#Preview {
    WallGridView()
}
