import SwiftUI
import AppKit

struct WallGridView: View {
    @State private var wallpaperBoxes: [WallpaperBox] = Array(0..<10).map { WallpaperBox(id: $0) }

    var body: some View {
        ScrollView(showsIndicators: false) {
            LazyVGrid(columns: [
                GridItem(.fixed(180), spacing: 10),
                GridItem(.fixed(180), spacing: 10)
            ], spacing: 10) {
                ForEach(wallpaperBoxes.indices, id: \.self) { index in
                    GridBoxView(wallpaperBox: $wallpaperBoxes[index])
                }
            }
        }
        .cornerRadius(35)
        .padding(10)
        .frame(width: 390, height: 270)
        .background(
            VisualEffectView(material: .hudWindow, blendingMode: .behindWindow)
        )
        .cornerRadius(40)
    }
}

struct WallpaperBox {
    let id: Int
    var selectedImage: NSImage?
    var imagePath: String?

    init(id: Int) {
        self.id = id
        self.selectedImage = nil
        self.imagePath = nil
    }
}

struct GridBoxView: View {
    @Binding var wallpaperBox: WallpaperBox

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 35)
                .fill(Color.white.opacity(0.10))
                .frame(width: 180, height: 120)

            if let selectedImage = wallpaperBox.selectedImage {
                // عرض الصورة المختارة
                Image(nsImage: selectedImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 180, height: 120)
                    .clipped()
                    .cornerRadius(35)
                    .onTapGesture {
                        applyWallpaper()
                    }
            } else {
                // مربع فارغ
                VStack {
                    Image(systemName: "plus.circle")
                        .font(.title)
                        .foregroundColor(.blue)
                    Text("مربع \(wallpaperBox.id + 1)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .onTapGesture {
                    chooseImage()
                }
            }
        }
        .contextMenu {
            if wallpaperBox.selectedImage != nil {
                Button("تطبيق كخلفية") {
                    applyWallpaper()
                }
                Button("حذف الصورة") {
                    wallpaperBox.selectedImage = nil
                    wallpaperBox.imagePath = nil
                }
                Button("اختيار صورة جديدة") {
                    chooseImage()
                }
            } else {
                Button("اختيار صورة") {
                    chooseImage()
                }
            }
        }
    }

    private func chooseImage() {
        let openPanel = NSOpenPanel()
        openPanel.title = "اختر صورة خلفية"
        openPanel.canChooseFiles = true
        openPanel.canChooseDirectories = false
        openPanel.allowsMultipleSelection = false
        openPanel.allowedContentTypes = [.image]

        openPanel.begin { response in
            if response == .OK, let url = openPanel.url {
                if let image = NSImage(contentsOf: url) {
                    wallpaperBox.selectedImage = image
                    wallpaperBox.imagePath = url.path
                }
            }
        }
    }

    private func applyWallpaper() {
        guard let imagePath = wallpaperBox.imagePath else { return }

        let url = URL(fileURLWithPath: imagePath)

        do {
            try NSWorkspace.shared.setDesktopImageURL(url, for: NSScreen.main!, options: [:])
            print("تم تطبيق الخلفية بنجاح!")
        } catch {
            print("خطأ في تطبيق الخلفية: \(error)")
        }
    }
}



// Visual Effect View لتطبيق تأثير blur
struct VisualEffectView: NSViewRepresentable {
    let material: NSVisualEffectView.Material
    let blendingMode: NSVisualEffectView.BlendingMode

    func makeNSView(context: Context) -> NSVisualEffectView {
        let visualEffectView = NSVisualEffectView()
        visualEffectView.material = material
        visualEffectView.blendingMode = blendingMode
        visualEffectView.state = .active
        return visualEffectView
    }

    func updateNSView(_ visualEffectView: NSVisualEffectView, context: Context) {
        visualEffectView.material = material
        visualEffectView.blendingMode = blendingMode
    }
}

#Preview {
    WallGridView()
}
