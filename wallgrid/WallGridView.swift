import SwiftUI
import AppKit

struct WallGridView: View {
    var body: some View {
        ScrollView(showsIndicators: false) {
            LazyVGrid(columns: [
                GridItem(.fixed(180), spacing: 10),
                GridItem(.fixed(180), spacing: 10)
            ], spacing: 10) {
                ForEach(0..<10, id: \.self) { index in
                    GridBoxView(index: index)
                }
            }
        }
        .cornerRadius(35)
        .padding(10)
        .frame(width: 390, height: 270)
        .background(
            VisualEffectView(material: .hudWindow, blendingMode: .behindWindow)
        )
        .cornerRadius(40)
    }
}

struct GridBoxView: View {
    let index: Int

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 35)
                .fill(Color.white.opacity(0.10))
                .frame(width: 180, height: 120)

            VStack {
                Image(systemName: "plus.circle")
                    .font(.title)
                    .foregroundColor(.blue)
                Text("مربع \(index + 1)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .onTapGesture {
                print("تم النقر على المربع رقم: \(index + 1)")
            }
        }
    }
}



// Visual Effect View لتطبيق تأثير blur
struct VisualEffectView: NSViewRepresentable {
    let material: NSVisualEffectView.Material
    let blendingMode: NSVisualEffectView.BlendingMode

    func makeNSView(context: Context) -> NSVisualEffectView {
        let visualEffectView = NSVisualEffectView()
        visualEffectView.material = material
        visualEffectView.blendingMode = blendingMode
        visualEffectView.state = .active
        return visualEffectView
    }

    func updateNSView(_ visualEffectView: NSVisualEffectView, context: Context) {
        visualEffectView.material = material
        visualEffectView.blendingMode = blendingMode
    }
}

#Preview {
    WallGridView()
}
